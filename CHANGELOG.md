# Change Log

All notable changes to this project will be documented in this file. See [versionize](https://github.com/versionize/versionize) for commit guidelines.

<a name="1.8.1"></a>
## [1.8.1](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/releases/tag/v1.8.1) (2025-07-27)

### Bug Fixes

* Add Missing Fonts ([0c9e916](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0c9e916aa7c4de1537b2d5f41b2e60ec9d5479f7))
* Remove GitHub Configuration Platforms ([add539d](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/add539dae3bd863d32b7b47157a65456fcedceef))
* Remove Versionize from CI/CD ([d979c8d](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d979c8d62e7ecdd98b7cda0aee1e33afcc9362ee))

<a name="1.8.0"></a>
## [1.8.0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/releases/tag/v1.8.0) (2025-07-26)

### Features

* Add login session management and prevent login duplication ([489f30d](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/489f30df6c3632f497774140110cf19375900da3))
* Add Missing Views On Web Project And Improve ManagementTeamService ([eab2fb2](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/eab2fb296bf423457562e77bb598efaf97b8ce37))
* Add TextBox for manual input in LeadTeamsNumericUpDown ([6fc245f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/6fc245fc42a1c2b2c17ed1677b9e569f5ddabc6c))
* Improve the release process ([69055a5](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/69055a5a016e8829e0086cfdc6d07f5ed6553489))
* Saving Login Credentials ([2a9f2c0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/2a9f2c074d7b6a7694ce320b0e1a157a67b3831e))
* Track and handle unsaved changes in the form ([241e67a](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/241e67abc2ed9da032b842be2bed8687b8051cd3))
* Update UI to reflect monitoring status changes ([0b48475](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0b48475fc7bf766eb03424b8418bd1204566214a))

### Bug Fixes

* Add new sync data tables for login sessions and screen access ([01da277](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/01da27794453b6d49593be87751c1fcf3946020e))
* Add SettingController and global using for SettingModel ([c478e16](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c478e162cb8ec4c602bda922114c030e511b6f4d))
* Fix Tracking Data Changes After Save ([1e59cb3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1e59cb3120ce8cb80bff9206b60434ddf38edbb9))

<a name="1.7.0"></a>
## [1.7.0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/releases/tag/v1.7.0) (2025-06-13)

### Features

* Add custom DateTimeConverter for UTC/local time handling ([da80c57](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/da80c577c53dd41969155d36c0c9c06ad5074ba6))
* Add password masking and visibility toggle features ([38a5fc2](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/38a5fc218bf7c3815f5d51396d6e003c838a533c))
* Support DateTime Filter For AttendanceLogReport And PayrollLogReport ([ba91bcf](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/ba91bcf67ee85735476730b97cd00e8c2d871c9e))
* Support DateTime Localization ([639bd7f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/639bd7ff9d74a6f993412e6a1a15a961fe227e7b))
* Update Tasks Changes From Database At EmployeeTrackerView ([6f5494a](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/6f5494ac67c59715cf3984a9f919d41c489148af))

### Bug Fixes

* Add conditional config for DataGridView based on Tag ([9aebe42](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/9aebe4287024c93ce4dd2a2a71add208e6152ebb))
* Add parameterless constructors for design-time support ([068067b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/068067bfc4c2964e071625c9f07070ef6ec77db6))
* Enhance PictureBoxSlider navigation and initialization ([5de295c](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5de295cf1e73272e038c0b440a7d3d589eff90a0))
* Fix Database Changes Notifications ([5709133](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5709133f6df715f8c92a3313e7a74d579be8240d))
* Fix ScreenShotsMonitoring Order List To Show Newer First ([befcd84](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/befcd84736dfbb24da2f9a7e3246425247d71194))
* Improve null and empty checks in EmployeeMapper.cs ([e902cbb](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/e902cbb860dd31a2f8dae87d053fae2bdde05069))
* Prevent multiple instances from opening the same project ([fc9b425](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fc9b4251b3b95da076e517920f1bf7f3ea37acfd))
* Refactor button state management with SetControls method ([c10b82c](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c10b82c28adf2f8097fc1bc8e95426351ce4b46f))
* Refactor client identification to use ClientId directly ([a792593](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a79259378ee6b47fe6d441913e34d3dc609370d7))
* Refactor repositories and update ordering logic ([048c7da](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/048c7da61feabb3d9607893999bec093e8df9762))
* Remove auto-login in non-debug scenarios ([461f2a1](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/461f2a1a2094dc1a2c25bc67876529d91a99ea22))
* Update Employee-User relationship and migration logic ([4218e60](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/4218e604d241b7617a9f321449fa47a552d1e220))
* Update MainView form title from "QuickPOS" to "LeadTeams" ([6b5431f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/6b5431f38316b029e6b7da4dbd683e3a02a02d6a))

<a name="1.6.0"></a>
## [1.6.0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/releases/tag/v1.6.0) (2025-05-20)

### Features

* Add .NET 8.0 runtime dependencies to deployment projects ([033aa02](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/033aa02a4f2385542edcc74318bcbee137be458c))
* Add async handling, loading indicators, and refactor UI ([0f5ad20](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0f5ad2092ea21381b3aabe3ca8faa5b941af3019))
* Add error handling for _baseService.Update/Add methods ([607b781](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/607b78146ad0bbf7d5f9fd90ce1b565502d9f305))
* Add event and property for combo box value changes ([e1dbe49](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/e1dbe49d63cf07eae744963c66cdf382aabdb21f))
* Add Salary And Currency Properties ([0ee8436](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0ee843668ebbdedd4094e9f938a7d0ad8d2e4fa1))
* Create API For LeadTeams Services ([84ac21a](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/84ac21a0d858b4998bc1fd4a4eb176e2244f38ab))
* Decuple DbContext From ISession Interface And Use The JWT Token Data ([7702f34](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/7702f3467c1d95adf57e50a5e0f446d314c178ba))
* Enhance AttendanceLogReport with time formatting ([c838082](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c838082449eaf8fe51e9da4b19864a005e5c56b7))
* Force Add OrganizationId To All ViewModels And Apply It On Web Project ([509867f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/509867f825715f6c041facf615b72b0772813a2d))
* Refactor EmployeeView layout and show selected shift details ([576c68f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/576c68fc11632180ca05aa6a54d75ecbd21bc025))
* Refactor user and access profile management ([32ea2c6](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/32ea2c63084a2fd9a01bbec3b9b9cda7ba607490))
* Switch To API Services On Desktop Applications ([a4ddbc9](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a4ddbc9b0e04009725f02eac05e15b12232ab39e))
* Take ScreenShot When Stop Working ([5925014](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5925014a32a16fefa593e659d4d3becd66777dd9))
* Update Shift Pattern Types To Last Enabled DateTime Value ([dac42a2](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/dac42a20a3a475c8e3e3790ca21ca238002792de))
* Use The TEntityCreate And TEntityUpdate Types For CU Operations From Desktop App ([fdf60ea](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fdf60ea2870b4baa880abbbcbc818d07fda9c6e2))

### Bug Fixes

* Add Missing AskLeaveStatus Property On AskLeaveMapper ([cf767e5](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/cf767e5092ac84d2c97606137ebefb02f80af92c))
* Add Missing EmployeeAllowanceMapper ([59dc958](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/59dc9585b09e4f632ca02ec0683057092b32a0cf))
* Add Missing Id Property And Mapping It To Fix ComboBox Selection ([6266c44](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/6266c444915336e96dd41e700b058d1a96dd688d))
* Add Missing IsActive Property To Mapped Models ([110d8a1](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/110d8a1a00dff569684c32d09ff7cfd4314c260f))
* Apply OrganizationId Value For List Properties On Model ([227f7af](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/227f7af41914565d86f31fe3d5db6cc68a53ac9c))
* Enhance combo box flexibility and simplify data handling ([223d5e9](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/223d5e90859bdb2b9d430f657a7e084626a53706))
* Enhance token refresh and authentication services ([35c2aac](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/35c2aac120cc18e40cfcb8b2061315f31eac5b4a))
* Fix AskLeave Actions ([c76cdcf](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c76cdcfa7e04d58b092c806bf7ee769bfbed5d1f))
* Fix LeadTeamsDateTimePicker font text layout ([47feb1f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/47feb1f8e8257cee1391d28079b10b07076acd1b))
* Fix Presenting The Screenshots ([5cfa7bd](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5cfa7bd740de1b0ecd9ed059a51910395429a1ca))
* Fix ScreenShotsMonitoringList Paginations ([e5fb8e8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/e5fb8e899433b857b8444ea2c342af8306c69725))
* Fix Typo ([64271fd](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/64271fd2b93e92146bca79d58eb1a136ef9f5be3))
* Fix Typo ([c4c2057](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c4c2057849e099f96179a97971a517efcda9d19c))
* Map Services To ILeadTeamsBaseService Interface ([14bfda4](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/14bfda48342fd39df2f15baa7cf9b20e07c95fe5))
* Map Services To ILeadTeamsBaseService Interface ([078e037](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/078e037d407cec6f51af06f5020fbf31be2a3a45))
* Refactor auth, middleware, and improve security ([678918e](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/678918e5b1309d5ba566b1ba17e5ea422f3750d6))
* Set single target framework and add custom app icon ([0956d37](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0956d3716e078892ef1372b29746d78522a0cb29))
* Update selection background colors in ColorsSchema.cs ([32f2810](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/32f28102bc847f455da640c5411c973e12ad9ccb))
* Use Main ILeadTeamsBaseService Interface ([d17ba23](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d17ba232dee11c5c1634a8d69b1080d1827447ee))

<a name="1.5.0"></a>
## [1.5.0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/releases/tag/v1.5.0) (2025-03-16)

### Features

* Abstract Session To LocalSession And CloudSession ([b953fd6](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/b953fd650e6f8e8ecb9eebe72521b8c79798ff8b))
* Add More Repositories And Services Methods ([f0ac626](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/f0ac626814bc11843870b6b1bb3b9b4e68ca5b62))
* Add Support To Assign SelectedValue From Ulid Type ([1532592](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1532592dbea8c588a8a6ad6bda26cfb901a2e888))
* Allow To DisposeAsync At SubscribeSession ([ceef2ca](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/ceef2cafefb3879e718e0af5e99a92bcaf4df25d))
* Apply The New Enumeration Class And Delete Enums ([3b86647](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/3b86647022bff186ecee5b7e970a1967f22e2e8e))
* Apply UI/UX ([3f5fb86](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/3f5fb8626a9fa3a4d146733299284e979340f797))
* Centralize The Entities Mapping ([1d4a667](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1d4a66794d5d7d650b87eae3d62e12afdbec4950))
* Convert UserType From Enum To Enumeration ([4c38cd0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/4c38cd0d247d9ef5482847cc90efe635763abaed))
* Create BaseRepository And BaseService To Handle LeadTeams Project Needs ([cd36f13](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/cd36f13eae301c833d29f9c022ad46a2c1048030))
* Create Logger And Apply Some Logs On Client Application ([4aef368](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/4aef368db37e2145f51d91452b96a8e84c7d0f9f))
* Enhancement Calculating Attendance Logs And Payrolls ([82d395a](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/82d395abdeeffcddd3d6ebe631d4eb71d5e6331a))
* Implement Pagination Support ([b8c412c](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/b8c412c27b9a1a0bd460a3fda70eaec171902002))
* Implement The New RepositorySpecifications Class ([3d25ea2](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/3d25ea290d0dde4d1a3f12c74af230d58d9e77af))
* Improve The Authentication Service ([00fad8b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/00fad8bcba669421ce46e7a65bff92f2c24febb7))
* Inherit All Controls From Base LeadTeams Controls To Apply Themes ([47b1f47](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/47b1f47438f61d806a10dec08eba635fad2fcb10))
* Support Dynamic Patterns Shift Type And Enhance Payroll Calculation Process ([207258b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/207258be819b0618a8a6b6a9a89d6f4f6fec234e))
* Switch To Guid As Primary Key For Support Data Sync ([1d5f18a](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1d5f18a42cd9a6268f79780d59840715565ee077))
* Use GMCadiom ModelValidation ([c0cae01](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c0cae01c4a605fcc18533d1b3d78ff88347811dc))
* Use The GMCadiom dgvListView ([0f1da5e](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0f1da5e4453f110e081e37d7b499f17e16aaab13))

### Bug Fixes

* Access TextBox Control, And Allow Null On Text Property ([2d47c62](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/2d47c62ab5a88ace20be994fee5dcab305476432))
* Add DefaultCommandTimeout To MySQL ConnectionString ([30b4405](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/30b44059cea265768b88c3d2442c5275dc7b7e01))
* Add Missing EmployeeViewModel Mapping Properties ([69d157a](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/69d157a9bd7738f65946853051343479c835ccd3))
* Add Missing Set RefreshTokenModel.OrganizationId Value ([32f99d6](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/32f99d6db32adb646770637cf3e4f6162a96209a))
* Always Migrate SqLite DBs ([676839f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/676839f7a00811ac704691b3d7aae914643f9fe2))
* Change SignalRChat Project Name ([9fd3c5f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/9fd3c5f10eed76f84d88db554c202d657120db48))
* CleanUp Unused Packaes And Code ([508ee87](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/508ee8744319b39abf0c5b0389c25d2338226f15))
* Clear ChangeTracker If Exception Has Been Thrown On Saving Database Changes ([4a4b76b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/4a4b76b4efcf03ed9caf6efd0b9ffa15869ca863))
* Collect All Failed Retuned Full View To One Method ([a1e811f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a1e811f7cc013ecd9c3bce2f1accd38d515ac154))
* Concat AttendanceLogDate AttendanceLogTime ([3ea5625](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/3ea5625cdda61571e15e65c6b7fc1e20390a4711))
* Create SQLite Database If Not Exists ([9058b01](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/9058b017c1d23e17720e0cb8685f365a3e569c44))
* Create SQLite Database If Not Exists ([0dff437](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0dff43723d22a2f3b45d0faa0213d10c90d40e6a))
* Delete AutoMapper Code ([d9851b0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d9851b01f2b4e01e9ab5d707e2b1f476d63215e4))
* Delete Duplicate Configurations From Presenter Layer ([d9ac19c](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d9ac19c3a1257a671329aea8aa4f52860638c7ff))
* Delete IsClient Flag From SetSession Method ([2b57684](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/2b5768469a48e31d5fe1647770ec57cc9e7ab509))
* Delete Trying To Open The Old Deleted LeadTeamsClientService ([5a9cc54](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5a9cc547c94578bec0f7b8120f62fd04993d5b2b))
* Delete Unnecessary Target Frameworks ([b428eef](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/b428eef950de7c60f1a8f97d8618e42d4970f2c0))
* Delete Unneeded Columns On Database ([38f3923](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/38f3923702ff36ce7e4b28370629c8ebaaf006df))
* Delete Unneeded ReflectionExtensions Code ([c1f895a](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c1f895ae4c5acb92a7629bf06308f57d9148dc3e))
* Delete Wrong Validation Of ShiftDynamicPatterns ([d3a93c9](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d3a93c936e4f2cdae124c7caaa5a55ffa0647ef4))
* Edit {Employee, Meeting} Models Before Save ([43e0ca4](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/43e0ca44de1311628376c4ee1e2595638e0614c3))
* Enhance Performance ([9eb51ca](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/9eb51ca051b0a33867a694d146bb0b704a02737b))
* Filter ViewModels With OrganizationId ([50718ee](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/50718ee7263c2a2869234f84cb76829e40d15608))
* Fix Binding On Custom Control ([30ce91e](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/30ce91e8095ac26fe8af3e5103fa42dc7202bfd6))
* Fix Checking TempProperty Null Before Saving To Database ([316b252](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/316b25258f2ac306dc8faaeadee8c2d9ab3fcda6))
* Fix Class Name Typo ([203b9f4](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/203b9f4d574916831d0399365eaeee6c1c157063))
* Fix DataTime Column Type DataAnnotations ([0d3a41d](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0d3a41dd276c390be4f7724838a1a2e0695d1f67))
* Fix DbContext DataBase Migration ([978e8c2](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/978e8c2ff39e1324117d97034dc52645f9dab225))
* Fix Duplication Of Binding Controls ([1852547](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/18525477fdb0edbea396fabcca80fb168d56feb1))
* Fix Editing Settings ([effb5c4](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/effb5c44b6b5d90593031e84942c13b612de8889))
* Fix Hub Casting And Notify DatabaseChanges ([04dd499](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/04dd4993dad1df29195948ad1a7be99d1c8395fb))
* Fix Loop Binding Same Control ([f7c8bc2](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/f7c8bc2d7d193a35d28fe10391e25722708a732e))
* Fix Notifying DataBase Changes ([a2117bb](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a2117bbcc63f4861b05dfb06c9d9b8d3d926fb08))
* Fix Null Settings And Initial Basic Settings For Organization ([782aa70](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/782aa70c1e01a2266f71610d4016d969136e89bb))
* Fix Saving Data To Database From EF Core ([c84753b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c84753b7af46d17eac6cdd7f65395d2322a3eb62))
* Fix ScreenShotsMonitoringDateTime Property Typo ([340d27f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/340d27f4caf79481d8517c3c792c01afc4087463))
* Fix Select MeetingWithEmployees Element Id ([7b14eb7](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/7b14eb71e760c603da6e6e3d5ec2876ada469151))
* Fix Showing Product Version ([9fca336](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/9fca336fd21ac65efdc07e2cffe666b261ef6e41))
* Fix Some Controls Properties And Improve Some Logic ([12f6091](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/12f60916f03604824583813b2209183f81374c1d))
* Fix Some DI By Using Direct Services ([b61e411](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/b61e4113447799db741c6546067d72434eaf2443))
* Fix Some Names Of Models ([c652685](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c6526851c5af506cc63085410852b462f0cbf58b))
* Fix Some Null Exceptions ([0af1f1f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0af1f1f7d5e4c4f1b148bd0c3835bcc868c9dc19))
* Fix Sync Exceptions For Casting Ulid To Binary Data Type ([2001482](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/2001482645f2bd968ad555a86f67f96732508086))
* Fix ViewModels Mapping ([bb69450](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/bb6945063cdc65fe22a46e9bf6210465d7e7fe5f))
* Fix Web Pagination Process ([262b1c8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/262b1c8db8173142f21f26e4839322a1027c1f4c))
* Fix Web Reports ([36a21e3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/36a21e3b88789c9cbc86f0121bf30afa4ce407a8))
* Force Subscribe Network Status To Check Internet Availability ([97f0b14](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/97f0b147689513c11e6e580293dd816e041dc0d0))
* Force Subscribe Network Status To Check Internet Availability ([2113c9d](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/2113c9d53b28b7f941aa09ddd69dcfd9265f4ae3))
* Getting Font Wiht Size And Style ([5b21d44](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5b21d445603906216f3b7d84b58d74ca0861a81c))
* Handle Potential Errors And Warnings ([0dc3700](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0dc3700e494c1f0ae65d7710a1c6a6fa1610697f))
* Improve Checking Internet Availability ([bb7d115](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/bb7d1151d762b54bfd8adafc4eaec62e442fe97c))
* Improve Checking Internet Availability ([6d0e1b2](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/6d0e1b23e61842d3effbaf1a52c51e6d7f17aa9f))
* Improve Database Sync ([74445ac](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/74445ace9e0270d2c14b30b22a2aba81717561b4))
* Improve Filling ComboBoxs ([a3f1dc2](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a3f1dc210c4041909f90b3b9d13721caff1afee7))
* Improve Handling ShiftDto Model ([9b571d1](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/9b571d150951d395c09bacecf99cb4688e0075af))
* Improve Possible Nulls Exception Handling ([8d5366f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/8d5366f5bf3caa60558cbd58949906b5a01dada3))
* Improve Query Performance ([74d58ec](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/74d58ec0a9783e6ca9e73756bc2b2e122a553c04))
* Improve The GetAllView Query ([99c2b3e](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/99c2b3e77dbfccf5172c84b4da17e5859a6ef238))
* Increase ScreenShotsMonitoringImageName MaxLength To 150 ([aaf747d](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/aaf747ddd9a95cc32cfd797cd71fe8bac5f70759))
* Make AskLeaveView Inherit From BaseEntryGenericView ([513dd9e](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/513dd9e80061f29349b671310cd98c580a495218))
* Make Sure DateTime Is Set Well ([5b5aa1b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5b5aa1b8e5dbcc7ff14ce025bd00b357c36d7fc1))
* Make Sure The PrimaryId And OrganizationId Are Set Probably ([6794bc5](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/6794bc545cff272bb01d5ba220bb4c18fe57ebaa))
* Prevent Ulid Casting Duplication ([5c14899](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5c14899ca07ede43218496b09faaff84f5636d59))
* Regenerate Database Migration ([f761afd](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/f761afd9b9f956f78462a5ff86520dd113ebe0b0))
* Regenerate Migrations ([e07884b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/e07884b10ce36a6d46223d8ff61261ea2b9739eb))
* Remove Including Employees On Fetching Shift Data ([7a80c38](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/7a80c38632e0227400b4681ccc69d4e870af6931))
* Set GlobalFilters To Filter OrganizationId At All Tables ([c9630f8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c9630f891f415e8c1ebb68cafa24f21285c7e605))
* Set OrganizationId At GlobalFilters Before Login ([e18a74d](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/e18a74d0cc7cd8baf7f71fdb9e406f0228ab44c7))
* Set The Missing OrganizationId Value At Saving ShiftModel Entity ([faf92b8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/faf92b8bd7b9225dfe6b200ef63190af1e7f0478))
* Show Logged In Employee Tasks Only ([a66e84c](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a66e84ca02d6445ea9543a9440b034bbdcb6240e))
* Support Subscription For DataBaseWatcher At Reports View ([75b96a5](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/75b96a5a74084bba67a041a169c5379255a23e93))
* Untrack AttendanceLog And ScreenShotsMonitoring From Auditing ([c7bf9a3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c7bf9a3c40bcbcd13440d0a4a82dc1590d9a93c9))
* Update API URL On launchSettings.json ([699fb90](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/699fb90297dacaac732f8ab0504b095caab36340))
* Update GMCadiom Project And Apply Changes ([67f660b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/67f660b9db90c871726a44998a5d2fe523448571))
* Update Packages ([978dc5f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/978dc5f7780ceb2645d9c1822880db1bc244f9fc))
* Use GMCadiom DataBaseWatcher ([560beb2](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/560beb208676e7a2b2274f6e9233b72416755232))

<a name="1.4.0"></a>
## [1.4.0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/releases/tag/v1.4.0) (2025-01-14)

### Features

* Add Ability To Send A Route Values On Pagination Parameters ([0cd2812](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0cd28125f34da0305b04387feb13c433194be500))
* Add Reports ([39228ec](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/39228eca4c4827fbc4952dc7747958f33aaf80f6))
* Add Shift ([7095287](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/70952870a99363811d7381ea05fe87d8d78ebd57))
* Delete Windows Service Project And ServerQueue Table ([1ff7b4f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1ff7b4fa0ff9a3b820f78a24bf504a9ef70c75bc))
* Implement SignalRChat Project ([f0275d4](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/f0275d4f9c012405a0aaa95407908551f8ec2bc3))
* Update GMCadiom Project And Upgrade GlobalFilter System ([1c1e200](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1c1e2004467afca9e1174c6f051c431739e3c0cd))

### Bug Fixes

* Fix Typo ([5c75640](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5c75640367a620b5b111663c4491c4a6781c4f02))
* Update The Database Server IP Address ([d550d56](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d550d560e294fe5de22e460291d27b504f21810a))

<a name="1.3.0"></a>
## [1.3.0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/releases/tag/v1.3.0) (2024-11-16)

### Features

* Add AskLeave Feature ([62e1b51](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/62e1b5197adaec90189f303731956777e7fd938c))
* Add AskLeave Feature To Client Application ([7c9ceb5](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/7c9ceb5549dcaef7368e27015472361f2d64e070))
* Add EmployeeWithPayrollLogReport ([5d0d6e3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5d0d6e3f1ee8ea9c5ce12d56b0d12d68365c6dc2))
* Add Husky ([46aaf49](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/46aaf4903e71ccf88352a599539d3735787c5b6d))
* Add JWT Bearer Authentication To Connect The API ([37c364e](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/37c364ec0f79dcc5ff847e56e4cd433df5a3dba2))
* Add More Options On Get Entity From DataBase ([73515bd](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/73515bd93a12c161ca5c6ffa23b401a931311e8b))
* Add NameSelector To All Repositories ([e180050](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/e180050c00d6ec1e668732b67badcf0a4643be0a))
* Allow User To Control The Columns Visibility In dgvViewList ([cad2de7](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/cad2de7327eaef01d122443b61090f6c8f01859d))
* Apply Global Query Filter By OrganizationId ([fce7f07](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fce7f07174061acccbd117387cde87cbfa49f78a))
* Auto Login If SaveLoginData Is True ([7a4485b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/7a4485b6c6d38778d5debb4ad6cf3750a25bd58a))
* Bind Shift.ShiftFixedPatterns To FixedPatterns Control ([e4e67f9](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/e4e67f948dd4bb5ef37f114fac6ed3aca381ac05))
* Check Shift On Each Period On AttendanceLog Report ([dba1022](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/dba10229bcb0c2888f7b53a9ff5ca0e7a5f1d8df))
* Create Audit Table ([a3893e8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a3893e8c77b41801517d6bde906e6c244ad92f03))
* Create Loading Splash Screen ([c094eeb](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c094eebfc8bb3d7ce59ecc48cdea5c7eebdcc15b))
* Create Meeting From Client Application And Add More Meeting Details ([94704f4](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/94704f4687efb0cc5fc12d3a4517fef2c61ec823))
* Create PictureBoxSlider To View More Details Of ScreenShotsMonitoring Entity ([a68a576](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a68a5763368a51a376a654f44b07985a52d1da2e))
* Create Settings Model For Setup Organization Configurations For Client Side ([38af45c](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/38af45c2516e445deb9d4ac8e531ffbdb7fd4384))
* Detach Repositories.Core From Repositories.EF ([060ab51](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/060ab5154ee94f855a99262123e1afeaa4d77eea))
* Flash Refresh Button When DataHasChanged Flagged With True Value ([0bedde6](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0bedde69bbaf217f465e3fcac1bf43fd6090f2a1))
* Implement INotifyPropertyChanged Interface To Entities Models ([4035cbc](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/4035cbcc89069c55f78fd3d2cc647e59be3619f9))
* Improve EmployeeWithAttendanceLogReport Which Show Duration / Target Hours ([afca0bb](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/afca0bb927c888e87933cbe1880176730f0a8800))
* Improve EmployeeWithAttendanceLogReport Which Show Log On Daily, Weekly, And Monthly View ([b1f3364](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/b1f336490def02db79fb0554098bcaeab1f043df))
* Inject Service In Presenter ([0984987](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/09849879353a0dd2449878b9838984e649a86560))
* Move And Implement GMCadiomCore Project As Git Modules ([950d4c8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/950d4c89acf48731193138f95cbda1848515184f))
* Notify From Database Watcher On Opened ListView And Refresh The Data ([34eb905](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/34eb905d1cf6be9ce4d4f9294a6ac50f9c7f9a21))
* ReArrange Models From PermissionAndSession And Delete Authentications Project ([09744bf](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/09744bf28df5a2c88e41a8b4849050695fefb2dd))
* Rebase Presenters And Set Object On Presenters Views ([9cdf020](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/9cdf020355f931a197f2d15b7fddfef744b54440))
* Refactor SignalR Service ([95b3252](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/95b325287c1376ba12b5a30faf412ade1f6c9995))
* Refactoring Shift Module ([2f0823d](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/2f0823de1a9f8015e45c8bc6109501045e8c048b))
* Save The Columns Visibility Configuration And Set It As Default View ([d2a32f0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d2a32f0c916afda4415d93c5264e7b1249b6a719))
* Start Last Task On Windows Boot ([bfeca39](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/bfeca391a2deb6e374f3a41e5df2f2e6eb1ad15a))
* Support SaveChangesAsync In DbContext ([a4f23d3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a4f23d38d5cb9b9b4d643e667524d6667ebeab29))
* Swap Authorization Header Key To jwtAccessToken Key ([2c0b101](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/2c0b101aa8de037e0854c945b5f70218f0268d87))
* Track Entities Changes ([026e236](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/026e23663027c33681eb8ed70b7df0010414ca79))
* Update BaseURL For API New MonsterASP.net Host ([21b0e1b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/21b0e1bdb68b6e007b71a645e6fbb5d096da2344))
* Update GMCadiomCore Project ([139e1c3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/139e1c3049203e04d7fc12409d605d0af609eb33))
* Upgrade TargetFramework To v8.0 And Update Nuget Packages ([f67f6c3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/f67f6c333ac4d80677c0dcaa04ec37811a070184))
* Use The GMCadiomCore Project Search Function ([5d45dd5](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5d45dd537de09525303751b0827730a7585d51bc))

### Bug Fixes

* Add Field For MinRandomScreenShot ([7b5a0ab](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/7b5a0ab3d2ef82599c4e414d01050d9e6670e6db))
* Add Missing SaveChanges To Update Database With Audit Records ([61290eb](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/61290eb91af90a81ae37eebb307b7a79379c2751))
* CleanUp ([d0c037b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d0c037bce4dfbe5eaa1991d9fb8186417ce748f9))
* Edit Or Update CurrentModel ([1a9bee7](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1a9bee79379f12c43f06ffefb0fd9cabd2bf4a5a))
* EnableRetryOnFailure On MySQLApplicationDBContext ([6f57123](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/6f571238a23af4e1cda2c2078fbe5a03a98ee9fc))
* Fetch All Reports With Order By Descending ([ed29470](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/ed294700778f28ffb04aca0af11273d079e6ae3b))
* Fix CreateJwtToken Iat Claim ([fb15c68](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fb15c687ab152d50cdaa4846de688611f8564ad3))
* Fix Cross Threading ([7648865](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/76488650a705bd5197d0699195abd0abe5aa6097))
* Fix Getting Nullable DateTime And TimeSpan From dtpBindingDateTimePicker ([0fe8231](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0fe823170b5ca4a6adafce5f928d163226179a10))
* Fix Getting Online Employee Synchronously ([2d04f6f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/2d04f6f0172332ec6bc51ebd354ffed8d0b3e822))
* Fix Getting SelectedValue From cmbBindingComboBox ([bebe236](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/bebe2361e5e96610fbc06737a9ddce75a179ab8f))
* Fix Getting Value From dtpBindingDateTimePicker ([5f89894](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5f8989428d99a078113ad444eaf42104fb42885b))
* Fix Multi Threading On dgvViewList ([421c9c0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/421c9c0e0d02e6b601fd1464f1b8d19810f286cf))
* Fix Null Exception ([b6dcfe9](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/b6dcfe9a9330fec0b244790735c724f11503a79f))
* Fix NullReferenceException On SyncBindingListExtensions ([1ceaad4](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1ceaad44b2d6e03c255e70e34eacd7459f99f468))
* Fix Refresh And Search Buttons ([3a9cad8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/3a9cad8aacb30727f9067e592d51fd6a278b841a))
* Fix Register Application On Windows Startup ([d9c65bd](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d9c65bd55d4364095ddf2e2370d31afb7c253663))
* Fix Save And Show ScreenShot Byte[] Value ([e1b64f7](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/e1b64f79929e41495717023a2e97e424ede5ed8b))
* Fix Some Spelling Typo ([1990186](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1990186d4ec894a71850fe36ede51e35dc0a3d5c))
* Fix Typo ([b9c324b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/b9c324be48da5b50fc059254daa7b945f41d57b8))
* Implement NameSpace Of Used JsonUtilities ([39c8c3e](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/39c8c3e44013b9f76635a2ad522c430c81eab247))
* Improve DataSourceHandler ([c8f96ef](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c8f96efa8443086e88296e31f742071d966b2f18))
* Improve Loading Data ([103016f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/103016f8558cba04aaff8c727fa3dcf2f7c36f46))
* Improve Loading Screen ([052996f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/052996fae2ffc977bb1191dfbf22c915a0b28018))
* Improve Performance Of Client Application ([fa4ca8a](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fa4ca8a8374843b25860735d796c08c8cc500deb))
* Improve Report Presenter ([a4c31a5](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a4c31a513e1f1158ab8f9f58fbd100a8da170ed8))
* Move All json Files To Model Project ([c04e318](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c04e3184159a13ac1e521dc3ce68cc0c8155242a))
* Preventing ReSet Filing Online Employees List ([c067ab3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c067ab347644ab507f16dd3b6ac5829557f3b3a3))
* Rearrange dgvViewList Pagination Buttons Location ([fbdca2f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fbdca2f50c00e405b45c69d6b09322feb7e14b11))
* Refactor And Rearrange AttendanceLogRepository Code ([afe3a13](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/afe3a132f5e1729ff68a13f785b7d2663ec0faaf))
* Release Control Focus On BaseFormView Before Actions Methods ([a7e11f6](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a7e11f6ff75030d4d01acb07730c5e7e93e17599))
* Remove Unneeded Properties ([363c17c](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/363c17cef066839b0caf87bcfa96947ff3311cab))
* ReSet Entities Relationships ([96d0640](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/96d0640a8d895fd10aaf59b1d275107f82e553fa))
* Set Default Value For Settings If It Null ([9c54df8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/9c54df8ed71c2fffa1f6e5e7174d86217f22ba78))
* Set Timeout To 60 Secs In MySQL ConnectionString ([4602252](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/460225235edc5774e1dad3fc5aa6146cfb632cf3))
* Show PopUps On The Main Thread In The Main Context ([14197d6](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/14197d6eab5b5043c8baf41cf4c0fb656c5807a3))
* Update Server URL And Credentials ([22a96dc](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/22a96dcbeb345e4ec4351833c25f2e60089bcd61))
* Use The BaseService.GetAllByName Function ([fa29fb2](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fa29fb2c377fe4a6697d78e927c50d5dfe875dd4))
* Use The Main LoadAllActionList From BaseListPresenter ([be7a88c](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/be7a88ce033002aa27d56b6b1eb8401af18deda6))

<a name="1.3.0"></a>
## [1.3.0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/releases/tag/v1.3.0) (2024-11-16)

### Features

* Add EmployeeWithPayrollLogReport ([5d0d6e3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5d0d6e3f1ee8ea9c5ce12d56b0d12d68365c6dc2))

### Bug Fixes

* EnableRetryOnFailure On MySQLApplicationDBContext ([6f57123](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/6f571238a23af4e1cda2c2078fbe5a03a98ee9fc))
* Implement NameSpace Of Used JsonUtilities ([39c8c3e](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/39c8c3e44013b9f76635a2ad522c430c81eab247))
* Set Timeout To 60 Secs In MySQL ConnectionString ([4602252](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/460225235edc5774e1dad3fc5aa6146cfb632cf3))

<a name="1.2.0"></a>
## [1.2.0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/releases/tag/v1.2.0) (2024-09-17)

### Features

* Add NameSelector To All Repositories ([e180050](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/e180050c00d6ec1e668732b67badcf0a4643be0a))
* Allow User To Control The Columns Visibility In dgvViewList ([cad2de7](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/cad2de7327eaef01d122443b61090f6c8f01859d))
* Create Loading Splash Screen ([c094eeb](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c094eebfc8bb3d7ce59ecc48cdea5c7eebdcc15b))
* Create Meeting From Client Application And Add More Meeting Details ([94704f4](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/94704f4687efb0cc5fc12d3a4517fef2c61ec823))
* Create PictureBoxSlider To View More Details Of ScreenShotsMonitoring Entity ([a68a576](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a68a5763368a51a376a654f44b07985a52d1da2e))
* Create Settings Model For Setup Organization Configurations For Client Side ([38af45c](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/38af45c2516e445deb9d4ac8e531ffbdb7fd4384))
* Flash Refresh Button When DataHasChanged Flagged With True Value ([0bedde6](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0bedde69bbaf217f465e3fcac1bf43fd6090f2a1))
* Inject Service In Presenter ([0984987](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/09849879353a0dd2449878b9838984e649a86560))
* Notify From Database Watcher On Opened ListView And Refresh The Data ([34eb905](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/34eb905d1cf6be9ce4d4f9294a6ac50f9c7f9a21))
* Save The Columns Visibility Configuration And Set It As Default View ([d2a32f0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d2a32f0c916afda4415d93c5264e7b1249b6a719))
* Support SaveChangesAsync In DbContext ([a4f23d3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a4f23d38d5cb9b9b4d643e667524d6667ebeab29))
* Update GMCadiomCore Project ([139e1c3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/139e1c3049203e04d7fc12409d605d0af609eb33))
* Use The GMCadiomCore Project Search Function ([5d45dd5](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5d45dd537de09525303751b0827730a7585d51bc))

### Bug Fixes

* Add Field For MinRandomScreenShot ([7b5a0ab](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/7b5a0ab3d2ef82599c4e414d01050d9e6670e6db))
* CleanUp ([d0c037b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d0c037bce4dfbe5eaa1991d9fb8186417ce748f9))
* Fetch All Reports With Order By Descending ([ed29470](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/ed294700778f28ffb04aca0af11273d079e6ae3b))
* Fix Cross Threading ([7648865](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/76488650a705bd5197d0699195abd0abe5aa6097))
* Fix Refresh And Search Buttons ([3a9cad8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/3a9cad8aacb30727f9067e592d51fd6a278b841a))
* Fix Typo ([b9c324b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/b9c324be48da5b50fc059254daa7b945f41d57b8))
* Improve DataSourceHandler ([c8f96ef](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c8f96efa8443086e88296e31f742071d966b2f18))
* Improve Loading Data ([103016f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/103016f8558cba04aaff8c727fa3dcf2f7c36f46))
* Improve Loading Screen ([052996f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/052996fae2ffc977bb1191dfbf22c915a0b28018))
* Improve Performance Of Client Application ([fa4ca8a](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fa4ca8a8374843b25860735d796c08c8cc500deb))
* Improve Report Presenter ([a4c31a5](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a4c31a513e1f1158ab8f9f58fbd100a8da170ed8))
* Rearrange dgvViewList Pagination Buttons Location ([fbdca2f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fbdca2f50c00e405b45c69d6b09322feb7e14b11))
* Remove Unneeded Properties ([363c17c](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/363c17cef066839b0caf87bcfa96947ff3311cab))
* Set Default Value For Settings If It Null ([9c54df8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/9c54df8ed71c2fffa1f6e5e7174d86217f22ba78))
* Show PopUps On The Main Thread In The Main Context ([14197d6](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/14197d6eab5b5043c8baf41cf4c0fb656c5807a3))
* Use The BaseService.GetAllByName Function ([fa29fb2](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fa29fb2c377fe4a6697d78e927c50d5dfe875dd4))
* Use The Main LoadAllActionList From BaseListPresenter ([be7a88c](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/be7a88ce033002aa27d56b6b1eb8401af18deda6))

<a name="1.1.0"></a>
## [1.1.0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/releases/tag/v1.1.0) (2024-08-29)

### Features

* Add Husky ([46aaf49](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/46aaf4903e71ccf88352a599539d3735787c5b6d))

### Other

* Add GitHub Actions Workflow For Automate Build And Release Process ([b282f98](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/b282f987d0c74b23d768457d2b6b2b164f947ae7))

<a name="1.0.0"></a>
## [1.0.0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/releases/tag/v1.0.0) (2024-08-28)

### Features

* Add AskLeave Feature ([62e1b51](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/62e1b5197adaec90189f303731956777e7fd938c))
* Add AskLeave Feature To Client Application ([7c9ceb5](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/7c9ceb5549dcaef7368e27015472361f2d64e070))
* Add JWT Bearer Authentication To Connect The API ([37c364e](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/37c364ec0f79dcc5ff847e56e4cd433df5a3dba2))
* Add More Options On Get Entity From DataBase ([73515bd](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/73515bd93a12c161ca5c6ffa23b401a931311e8b))
* Apply Global Query Filter By OrganizationId ([fce7f07](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fce7f07174061acccbd117387cde87cbfa49f78a))
* Auto Login If SaveLoginData Is True ([7a4485b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/7a4485b6c6d38778d5debb4ad6cf3750a25bd58a))
* Bind Shift.ShiftFixedPatterns To FixedPatterns Control ([e4e67f9](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/e4e67f948dd4bb5ef37f114fac6ed3aca381ac05))
* Check Shift On Each Period On AttendanceLog Report ([dba1022](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/dba10229bcb0c2888f7b53a9ff5ca0e7a5f1d8df))
* Create Audit Table ([a3893e8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a3893e8c77b41801517d6bde906e6c244ad92f03))
* Detach Repositories.Core From Repositories.EF ([060ab51](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/060ab5154ee94f855a99262123e1afeaa4d77eea))
* Implement INotifyPropertyChanged Interface To Entities Models ([4035cbc](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/4035cbcc89069c55f78fd3d2cc647e59be3619f9))
* Improve EmployeeWithAttendanceLogReport Which Show Duration / Target Hours ([afca0bb](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/afca0bb927c888e87933cbe1880176730f0a8800))
* Improve EmployeeWithAttendanceLogReport Which Show Log On Daily, Weekly, And Monthly View ([b1f3364](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/b1f336490def02db79fb0554098bcaeab1f043df))
* Move And Implement GMCadiomCore Project As Git Modules ([950d4c8](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/950d4c89acf48731193138f95cbda1848515184f))
* ReArrange Models From PermissionAndSession And Delete Authentications Project ([09744bf](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/09744bf28df5a2c88e41a8b4849050695fefb2dd))
* Rebase Presenters And Set Object On Presenters Views ([9cdf020](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/9cdf020355f931a197f2d15b7fddfef744b54440))
* Refactor SignalR Service ([95b3252](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/95b325287c1376ba12b5a30faf412ade1f6c9995))
* Refactoring Shift Module ([2f0823d](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/2f0823de1a9f8015e45c8bc6109501045e8c048b))
* Start Last Task On Windows Boot ([bfeca39](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/bfeca391a2deb6e374f3a41e5df2f2e6eb1ad15a))
* Swap Authorization Header Key To jwtAccessToken Key ([2c0b101](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/2c0b101aa8de037e0854c945b5f70218f0268d87))
* Track Entities Changes ([026e236](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/026e23663027c33681eb8ed70b7df0010414ca79))
* Update BaseURL For API New MonsterASP.net Host ([21b0e1b](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/21b0e1bdb68b6e007b71a645e6fbb5d096da2344))
* Upgrade TargetFramework To v8.0 And Update Nuget Packages ([f67f6c3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/f67f6c333ac4d80677c0dcaa04ec37811a070184))

### Bug Fixes

* Add Missing SaveChanges To Update Database With Audit Records ([61290eb](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/61290eb91af90a81ae37eebb307b7a79379c2751))
* Edit Or Update CurrentModel ([1a9bee7](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1a9bee79379f12c43f06ffefb0fd9cabd2bf4a5a))
* Fix CreateJwtToken Iat Claim ([fb15c68](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/fb15c687ab152d50cdaa4846de688611f8564ad3))
* Fix Getting Nullable DateTime And TimeSpan From dtpBindingDateTimePicker ([0fe8231](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/0fe823170b5ca4a6adafce5f928d163226179a10))
* Fix Getting Online Employee Synchronously ([2d04f6f](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/2d04f6f0172332ec6bc51ebd354ffed8d0b3e822))
* Fix Getting SelectedValue From cmbBindingComboBox ([bebe236](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/bebe2361e5e96610fbc06737a9ddce75a179ab8f))
* Fix Getting Value From dtpBindingDateTimePicker ([5f89894](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/5f8989428d99a078113ad444eaf42104fb42885b))
* Fix Multi Threading On dgvViewList ([421c9c0](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/421c9c0e0d02e6b601fd1464f1b8d19810f286cf))
* Fix Null Exception ([b6dcfe9](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/b6dcfe9a9330fec0b244790735c724f11503a79f))
* Fix NullReferenceException On SyncBindingListExtensions ([1ceaad4](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1ceaad44b2d6e03c255e70e34eacd7459f99f468))
* Fix Register Application On Windows Startup ([d9c65bd](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/d9c65bd55d4364095ddf2e2370d31afb7c253663))
* Fix Save And Show ScreenShot Byte[] Value ([e1b64f7](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/e1b64f79929e41495717023a2e97e424ede5ed8b))
* Fix Some Spelling Typo ([1990186](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/1990186d4ec894a71850fe36ede51e35dc0a3d5c))
* Move All json Files To Model Project ([c04e318](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c04e3184159a13ac1e521dc3ce68cc0c8155242a))
* Preventing ReSet Filing Online Employees List ([c067ab3](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/c067ab347644ab507f16dd3b6ac5829557f3b3a3))
* Refactor And Rearrange AttendanceLogRepository Code ([afe3a13](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/afe3a132f5e1729ff68a13f785b7d2663ec0faaf))
* Release Control Focus On BaseFormView Before Actions Methods ([a7e11f6](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/a7e11f6ff75030d4d01acb07730c5e7e93e17599))
* ReSet Entities Relationships ([96d0640](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/96d0640a8d895fd10aaf59b1d275107f82e553fa))
* Update Server URL And Credentials ([22a96dc](https://www.github.com/GMCadiom-SherifShalaby/LeadTeams/commit/22a96dcbeb345e4ec4351833c25f2e60089bcd61))

