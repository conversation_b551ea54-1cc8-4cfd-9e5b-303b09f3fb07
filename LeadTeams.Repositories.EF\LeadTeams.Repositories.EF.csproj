<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Version>1.8.1</Version>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0-windows|AnyCPU'">
    <NoWarn>1701;1702;CA1416</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0-windows|AnyCPU'">
    <NoWarn>1701;1702;CA1416</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Dotmim.Sync.Sqlite" Version="1.3.0-beta-1819" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.14" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.2">
      <TreatAsUsed>true</TreatAsUsed>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <TreatAsUsed>true</TreatAsUsed>
	</PackageReference>
	<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <TreatAsUsed>true</TreatAsUsed>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Repositories.EF\GMCadiomCore.Repositories.EF.csproj" />
    <ProjectReference Include="..\LeadTeams.Models\LeadTeams.Models.csproj" />
    <ProjectReference Include="..\LeadTeams.PermissionAndSession\LeadTeams.PermissionAndSession.csproj" />
    <ProjectReference Include="..\LeadTeams.Repositories.Core\LeadTeams.Repositories.Core.csproj" />
    <ProjectReference Include="..\LeadTeams.Shared\LeadTeams.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Migrations\MySQLMigrations\" />
    <Folder Include="Migrations\SQLiteMigrations\" />
  </ItemGroup>
  <Target Name="Husky" BeforeTargets="Restore;CollectPackageReferences" Condition="'$(HUSKY)' != 0 and '$(IsCrossTargetingBuild)' == 'true'">
    <Exec Command="dotnet tool restore" StandardOutputImportance="Low" StandardErrorImportance="High" />
    <Exec Command="dotnet husky install" StandardOutputImportance="Low" StandardErrorImportance="High" WorkingDirectory=".." />
  </Target>
</Project>