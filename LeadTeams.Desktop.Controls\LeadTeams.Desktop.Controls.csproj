<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <Version>1.8.1</Version>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Desktop\GMCadiomCore.Desktop.csproj" />
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Models\GMCadiomCore.Models.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="CombinedControls\LeadTeamsLabeledComboBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="CombinedControls\LeadTeamsLabeledDateTimePicker.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="CombinedControls\LeadTeamsLabeledLabel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="CombinedControls\LeadTeamsLabeledNumericUpDown.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="CombinedControls\LeadTeamsLabeledTextBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsCheckBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsCheckedListBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsComboBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsContextMenuStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsDataGridView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsDateTimePicker.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsGroupBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsListBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsNumericUpDown.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsPictureBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsRadioButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsStatusStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsTabControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsTableLayoutPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsTabPage.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsToolStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsToolStripButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsToolStripDropDownButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsToolStripMenuItem.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsToolStripSeparator.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="LeadTeamsControls\LeadTeamsToolStripStatusLabel.cs">
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="Core\Helper\multicolore-pro.regular.otf" TargetPath="fonts\multicolore-pro.regular.otf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Core\Helper\novus.bold.otf" TargetPath="fonts\novus.bold.otf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Core\Helper\novus.light.otf" TargetPath="fonts\novus.light.otf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Core\Helper\novus.regular.otf" TargetPath="fonts\novus.regular.otf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
