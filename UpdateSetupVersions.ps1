# PowerShell script to update version information in Visual Studio Setup Projects
# This script helps ensure proper MSI upgrade behavior by updating ProductCode and ProductVersion

param(
    [Parameter(Mandatory=$false)]
    [string]$NewVersion = "",
    [Parameter(Mandatory=$false)]
    [switch]$AutoIncrement = $false,
    [Parameter(Mandatory=$false)]
    [switch]$SyncFromProjects = $false
)

function New-Guid {
    return [System.Guid]::NewGuid().ToString().ToUpper()
}

function Update-SetupProject {
    param(
        [string]$ProjectPath,
        [string]$NewVersion,
        [string]$NewProductCode
    )

    Write-Host "Updating setup project: $ProjectPath" -ForegroundColor Green

    # Read the project file
    $content = Get-Content $ProjectPath -Raw

    # Debug: Show current version before update
    if ($content -match '"ProductVersion" = "8:([^"]*)"') {
        Write-Host "  - Current ProductVersion: $($matches[1])" -ForegroundColor Gray
    }

    # Update ProductVersion
    $originalContent = $content
    $content = $content -replace '"ProductVersion" = "8:[^"]*"', "`"ProductVersion`" = `"8:$NewVersion`""

    # Debug: Check if replacement worked
    if ($content -eq $originalContent) {
        Write-Warning "  - ProductVersion replacement did not change the content"
    }

    # Update ProductCode (generate new GUID)
    $content = $content -replace '"ProductCode" = "8:\{[^}]*\}"', "`"ProductCode`" = `"8:{$NewProductCode}`""

    # Write back to file
    try {
        Set-Content -Path $ProjectPath -Value $content -NoNewline -Encoding UTF8
        Write-Host "  - File written successfully" -ForegroundColor Gray
    }
    catch {
        Write-Error "  - Failed to write file: $($_.Exception.Message)"
        throw
    }

    Write-Host "  - Updated ProductVersion to: $NewVersion" -ForegroundColor Yellow
    Write-Host "  - Updated ProductCode to: {$NewProductCode}" -ForegroundColor Yellow
}

function Get-CurrentVersion {
    param([string]$ProjectPath)
    
    $content = Get-Content $ProjectPath -Raw
    if ($content -match '"ProductVersion" = "8:([^"]*)"') {
        return $matches[1]
    }
    return "1.0.0"
}

function Get-ProjectVersion {
    param([string]$ProjectPath)

    if (-not (Test-Path $ProjectPath)) {
        Write-Warning "Project file not found: $ProjectPath"
        return $null
    }

    try {
        [xml]$projectXml = Get-Content $ProjectPath

        # Try to find Version in any PropertyGroup
        $versionNodes = $projectXml.SelectNodes("//PropertyGroup/Version")
        if ($versionNodes -and $versionNodes.Count -gt 0) {
            $version = $versionNodes[0].InnerText
            if ($version) {
                return $version.Trim()
            }
        }

        # Alternative approach using XPath
        $versionNode = $projectXml.SelectSingleNode("//Version")
        if ($versionNode -and $versionNode.InnerText) {
            return $versionNode.InnerText.Trim()
        }

        Write-Warning "No version found in project file: $ProjectPath"
        return $null
    }
    catch {
        Write-Error "Failed to read project file: $ProjectPath. Error: $($_.Exception.Message)"
        return $null
    }
}

function Increment-Version {
    param([string]$Version)

    $parts = $Version.Split('.')
    if ($parts.Length -ge 3) {
        $major = [int]$parts[0]
        $minor = [int]$parts[1]
        $build = [int]$parts[2]
        $build++
        return "$major.$minor.$build"
    }
    return "1.0.1"
}

# Main script logic
$clientSetupPath = "LeadTeamsClientSetup\LeadTeamsClientSetup.vdproj"
$desktopSetupPath = "LeadTeamsDesktopSetup\LeadTeamsDesktopSetup.vdproj"
$clientProjectPath = "LeadTeams.Client\LeadTeams.Client.csproj"
$desktopProjectPath = "LeadTeams.Desktop\LeadTeams.Desktop.csproj"

# Check if setup project files exist
if (-not (Test-Path $clientSetupPath)) {
    Write-Error "Client setup project not found: $clientSetupPath"
    exit 1
}

if (-not (Test-Path $desktopSetupPath)) {
    Write-Error "Desktop setup project not found: $desktopSetupPath"
    exit 1
}

# Determine the new version
if ($SyncFromProjects) {
    # Get version from the main projects (they should be the same after versionize)
    $clientProjectVersion = Get-ProjectVersion $clientProjectPath
    $desktopProjectVersion = Get-ProjectVersion $desktopProjectPath

    if ($clientProjectVersion -and $desktopProjectVersion) {
        if ($clientProjectVersion -eq $desktopProjectVersion) {
            $NewVersion = $clientProjectVersion
            Write-Host "Syncing setup projects to version $NewVersion from main projects" -ForegroundColor Cyan
        } else {
            Write-Warning "Project versions don't match! Client: $clientProjectVersion, Desktop: $desktopProjectVersion"
            Write-Host "Using Client project version: $clientProjectVersion" -ForegroundColor Yellow
            $NewVersion = $clientProjectVersion
        }
    } elseif ($clientProjectVersion) {
        $NewVersion = $clientProjectVersion
        Write-Host "Using Client project version: $NewVersion" -ForegroundColor Cyan
    } elseif ($desktopProjectVersion) {
        $NewVersion = $desktopProjectVersion
        Write-Host "Using Desktop project version: $NewVersion" -ForegroundColor Cyan
    } else {
        Write-Error "Could not read version from either project file"
        exit 1
    }
} elseif ($AutoIncrement) {
    $currentClientVersion = Get-CurrentVersion $clientSetupPath
    $NewVersion = Increment-Version $currentClientVersion
    Write-Host "Auto-incrementing version from $currentClientVersion to $NewVersion" -ForegroundColor Cyan
} elseif ([string]::IsNullOrEmpty($NewVersion)) {
    $currentClientVersion = Get-CurrentVersion $clientSetupPath
    $NewVersion = Read-Host "Enter new version (current: $currentClientVersion)"
    if ([string]::IsNullOrEmpty($NewVersion)) {
        $NewVersion = Increment-Version $currentClientVersion
        Write-Host "Using auto-incremented version: $NewVersion" -ForegroundColor Cyan
    }
}

# Validate version format
if ($NewVersion -notmatch '^\d+\.\d+\.\d+$') {
    Write-Error "Invalid version format: $NewVersion. Expected format: x.y.z (e.g., 1.2.3)"
    exit 1
}

# Generate new ProductCodes
$newClientProductCode = New-Guid
$newDesktopProductCode = New-Guid

Write-Host "`nUpdating setup projects with version $NewVersion..." -ForegroundColor Magenta

# Update both setup projects
try {
    Update-SetupProject -ProjectPath $clientSetupPath -NewVersion $NewVersion -NewProductCode $newClientProductCode
    Update-SetupProject -ProjectPath $desktopSetupPath -NewVersion $NewVersion -NewProductCode $newDesktopProductCode

    Write-Host "`nSetup projects updated successfully!" -ForegroundColor Green

    if ($SyncFromProjects) {
        Write-Host "Setup project versions have been synchronized with main project versions." -ForegroundColor Green
    }

    Write-Host "Remember to rebuild both setup projects to generate new MSI files." -ForegroundColor Yellow
}
catch {
    Write-Error "Failed to update setup projects: $($_.Exception.Message)"
    exit 1
}

# Display summary
Write-Host "`n=== UPDATE SUMMARY ===" -ForegroundColor Magenta
Write-Host "Version: $NewVersion" -ForegroundColor White
if ($SyncFromProjects) {
    Write-Host "Source: Synchronized from main projects" -ForegroundColor White
}
Write-Host "Client Setup Project: $clientSetupPath" -ForegroundColor White
Write-Host "Desktop Setup Project: $desktopSetupPath" -ForegroundColor White
Write-Host "Client ProductCode: {$newClientProductCode}" -ForegroundColor White
Write-Host "Desktop ProductCode: {$newDesktopProductCode}" -ForegroundColor White
Write-Host "======================" -ForegroundColor Magenta
