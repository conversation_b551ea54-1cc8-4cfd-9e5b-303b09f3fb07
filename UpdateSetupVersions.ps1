# PowerShell script to update version information in Visual Studio Setup Projects
# This script helps ensure proper MSI upgrade behavior by updating ProductCode and ProductVersion

param(
    [Parameter(Mandatory=$false)]
    [string]$NewVersion = "",
    [Parameter(Mandatory=$false)]
    [switch]$AutoIncrement = $false
)

function New-Guid {
    return [System.Guid]::NewGuid().ToString().ToUpper()
}

function Update-SetupProject {
    param(
        [string]$ProjectPath,
        [string]$NewVersion,
        [string]$NewProductCode
    )
    
    Write-Host "Updating setup project: $ProjectPath" -ForegroundColor Green
    
    # Read the project file
    $content = Get-Content $ProjectPath -Raw
    
    # Update ProductVersion
    $content = $content -replace '"ProductVersion" = "8:[^"]*"', "`"ProductVersion`" = `"8:$NewVersion`""
    
    # Update ProductCode (generate new GUID)
    $content = $content -replace '"ProductCode" = "8:\{[^}]*\}"', "`"ProductCode`" = `"8:{$NewProductCode}`""
    
    # Write back to file
    Set-Content -Path $ProjectPath -Value $content -NoNewline
    
    Write-Host "  - Updated ProductVersion to: $NewVersion" -ForegroundColor Yellow
    Write-Host "  - Updated ProductCode to: {$NewProductCode}" -ForegroundColor Yellow
}

function Get-CurrentVersion {
    param([string]$ProjectPath)
    
    $content = Get-Content $ProjectPath -Raw
    if ($content -match '"ProductVersion" = "8:([^"]*)"') {
        return $matches[1]
    }
    return "1.0.0"
}

function Increment-Version {
    param([string]$Version)
    
    $parts = $Version.Split('.')
    if ($parts.Length -ge 3) {
        $major = [int]$parts[0]
        $minor = [int]$parts[1]
        $build = [int]$parts[2]
        $build++
        return "$major.$minor.$build"
    }
    return "1.0.1"
}

# Main script logic
$clientSetupPath = "LeadTeamsClientSetup\LeadTeamsClientSetup.vdproj"
$desktopSetupPath = "LeadTeamsDesktopSetup\LeadTeamsDesktopSetup.vdproj"

# Check if setup project files exist
if (-not (Test-Path $clientSetupPath)) {
    Write-Error "Client setup project not found: $clientSetupPath"
    exit 1
}

if (-not (Test-Path $desktopSetupPath)) {
    Write-Error "Desktop setup project not found: $desktopSetupPath"
    exit 1
}

# Determine the new version
if ($AutoIncrement) {
    $currentClientVersion = Get-CurrentVersion $clientSetupPath
    $NewVersion = Increment-Version $currentClientVersion
    Write-Host "Auto-incrementing version from $currentClientVersion to $NewVersion" -ForegroundColor Cyan
} elseif ([string]::IsNullOrEmpty($NewVersion)) {
    $currentClientVersion = Get-CurrentVersion $clientSetupPath
    $NewVersion = Read-Host "Enter new version (current: $currentClientVersion)"
    if ([string]::IsNullOrEmpty($NewVersion)) {
        $NewVersion = Increment-Version $currentClientVersion
        Write-Host "Using auto-incremented version: $NewVersion" -ForegroundColor Cyan
    }
}

# Generate new ProductCodes
$newClientProductCode = New-Guid
$newDesktopProductCode = New-Guid

Write-Host "`nUpdating setup projects with version $NewVersion..." -ForegroundColor Magenta

# Update both setup projects
Update-SetupProject -ProjectPath $clientSetupPath -NewVersion $NewVersion -NewProductCode $newClientProductCode
Update-SetupProject -ProjectPath $desktopSetupPath -NewVersion $NewVersion -NewProductCode $newDesktopProductCode

Write-Host "`nSetup projects updated successfully!" -ForegroundColor Green
Write-Host "Remember to rebuild both setup projects to generate new MSI files." -ForegroundColor Yellow

# Display summary
Write-Host "`n=== UPDATE SUMMARY ===" -ForegroundColor Magenta
Write-Host "Version: $NewVersion" -ForegroundColor White
Write-Host "Client ProductCode: {$newClientProductCode}" -ForegroundColor White
Write-Host "Desktop ProductCode: {$newDesktopProductCode}" -ForegroundColor White
Write-Host "======================" -ForegroundColor Magenta
