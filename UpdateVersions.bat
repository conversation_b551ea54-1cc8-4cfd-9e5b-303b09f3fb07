@echo off
echo ========================================
echo   LeadTeams Setup Version Updater
echo ========================================
echo.
echo This script will update the version information
echo in both setup projects to ensure proper MSI
echo upgrade behavior.
echo.
echo Options:
echo 1. Sync from main projects (recommended after versionize)
echo 2. Auto-increment current version
echo 3. Manual version entry
echo.

set /p choice="Enter your choice (1-3): "

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PowerShell is not available on this system.
    echo Please run the UpdateSetupVersions.ps1 script manually.
    pause
    exit /b 1
)

REM Run the PowerShell script based on choice
echo Running version update script...
if "%choice%"=="1" (
    echo Synchronizing versions from main projects...
    powershell -ExecutionPolicy Bypass -File "UpdateSetupVersions.ps1" -SyncFromProjects
) else if "%choice%"=="2" (
    echo Auto-incrementing version...
    powershell -ExecutionPolicy Bypass -File "UpdateSetupVersions.ps1" -AutoIncrement
) else (
    echo Manual version entry...
    powershell -ExecutionPolicy Bypass -File "UpdateSetupVersions.ps1"
)

if errorlevel 1 (
    echo.
    echo ERROR: Failed to update setup projects.
    pause
    exit /b 1
)

echo.
echo SUCCESS: Setup projects have been updated!
echo.
echo Next steps:
echo 1. Rebuild both setup projects in Visual Studio
echo 2. Test the new MSI installers
echo.
pause
