@echo off
echo ========================================
echo   LeadTeams Setup Version Updater
echo ========================================
echo.
echo This script will update the version information
echo in both setup projects to ensure proper MSI
echo upgrade behavior.
echo.

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PowerShell is not available on this system.
    echo Please run the UpdateSetupVersions.ps1 script manually.
    pause
    exit /b 1
)

REM Run the PowerShell script with auto-increment
echo Running version update script...
powershell -ExecutionPolicy Bypass -File "UpdateSetupVersions.ps1" -AutoIncrement

if errorlevel 1 (
    echo.
    echo ERROR: Failed to update setup projects.
    pause
    exit /b 1
)

echo.
echo SUCCESS: Setup projects have been updated!
echo.
echo Next steps:
echo 1. Rebuild both setup projects in Visual Studio
echo 2. Test the new MSI installers
echo.
pause
