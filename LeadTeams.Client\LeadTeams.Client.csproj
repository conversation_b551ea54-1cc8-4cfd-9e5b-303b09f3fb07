<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <StartupObject>LeadTeams.Client.Program</StartupObject>
    <ApplicationIcon>icons8-monitoring-96.ico</ApplicationIcon>
    <Version>1.8.1</Version>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="icons8-monitoring-96.ico" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Desktop\GMCadiomCore.Desktop.csproj" />
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Streaming\GMCadiomCore.Streaming.csproj" />
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Tools\GMCadiomCore.Tools.csproj" />
    <ProjectReference Include="..\LeadTeams.DbSync\LeadTeams.DbSync.csproj" />
    <ProjectReference Include="..\LeadTeams.Desktop.Services\LeadTeams.Desktop.Services.csproj" />
    <ProjectReference Include="..\LeadTeams.Services.API\LeadTeams.Services.API.csproj" />
    <ProjectReference Include="..\LeadTeams.Shared.Windows\LeadTeams.Shared.Windows.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Properties\Settings.Designer.cs">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Update="View\AskLeaveListView.cs" />
    <Compile Update="View\AskLeaveView.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Update="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <Target Name="Husky" BeforeTargets="Restore;CollectPackageReferences" Condition="'$(HUSKY)' != 0">
    <Exec Command="dotnet tool restore" StandardOutputImportance="Low" StandardErrorImportance="High" />
    <Exec Command="dotnet husky install" StandardOutputImportance="Low" StandardErrorImportance="High" WorkingDirectory=".." />
  </Target>
</Project>