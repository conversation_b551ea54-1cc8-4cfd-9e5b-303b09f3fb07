# LeadTeams Release Process

This document describes the improved automated release process for the LeadTeams project, which includes automatic version synchronization between main projects and setup projects.

## Overview

The release process has been enhanced to:
1. Use `versionize` to automatically increment project versions based on conventional commits
2. Automatically sync setup project versions with main project versions
3. Build setup projects with synchronized versions
4. Create GitHub releases with properly versioned installers

## Automated Release Process (GitHub Actions)

### Trigger
The automated release process is triggered by:
- Push to the `master` branch
- Manual workflow dispatch

### Process Flow
1. **Build Environment Setup**
   - Checkout code with submodules
   - Setup .NET 8 SDK and Visual Studio build tools
   - Restore dependencies

2. **Version Management**
   - Run `versionize` to analyze commits and increment versions
   - If significant changes are found, versionize updates:
     - Project versions in `.csproj` files
     - `CHANGELOG.md` with new version and changes
     - Creates a git tag

3. **Setup Project Synchronization**
   - Display current project versions for debugging
   - Run `UpdateSetupVersions.ps1 -SyncFromProjects` to:
     - Read versions from `LeadTeams.Client.csproj` and `LeadTeams.Desktop.csproj`
     - Update `ProductVersion` in both setup projects
     - Generate new `ProductCode` GUIDs for proper MSI upgrade behavior
   - Commit setup project changes

4. **Build and Package**
   - Build both setup projects using Visual Studio devenv
   - Create ZIP packages for distribution

5. **Release Creation**
   - Push all changes and tags to GitHub
   - Create GitHub release with auto-generated release notes
   - Upload setup packages as release assets

## Manual Release Process

### Option 1: Sync from Projects (Recommended after versionize)
```bash
# After running versionize manually
.\UpdateVersions.bat
# Choose option 1: Sync from main projects
```

Or directly:
```powershell
.\UpdateSetupVersions.ps1 -SyncFromProjects
```

### Option 2: Auto-increment
```powershell
.\UpdateSetupVersions.ps1 -AutoIncrement
```

### Option 3: Manual version entry
```powershell
.\UpdateSetupVersions.ps1
# Enter version when prompted
```

## Version Synchronization Details

### How It Works
The `UpdateSetupVersions.ps1` script:
1. Reads version from main project `.csproj` files using XML parsing
2. Updates `ProductVersion` in both setup project `.vdproj` files
3. Generates new `ProductCode` GUIDs to ensure proper MSI upgrade behavior
4. Validates version format (x.y.z)

### Files Updated
- `LeadTeamsClientSetup/LeadTeamsClientSetup.vdproj`
- `LeadTeamsDesktopSetup/LeadTeamsDesktopSetup.vdproj`

### Version Sources
- `LeadTeams.Client/LeadTeams.Client.csproj`
- `LeadTeams.Desktop/LeadTeams.Desktop.csproj`

## Conventional Commits

To trigger automatic version increments, use conventional commit messages:

### Version Increments
- **Patch** (1.0.0 → 1.0.1): `fix:`, `docs:`, `style:`, `refactor:`, `test:`, `chore:`
- **Minor** (1.0.0 → 1.1.0): `feat:`
- **Major** (1.0.0 → 2.0.0): Any commit with `BREAKING CHANGE:` in footer

### Examples
```bash
# Patch release
git commit -m "fix: resolve login timeout issue"

# Minor release  
git commit -m "feat: add new employee dashboard"

# Major release
git commit -m "feat: redesign authentication system

BREAKING CHANGE: API endpoints have changed"
```

## Troubleshooting

### Common Issues

1. **"No release required" message**
   - No significant commits since last release
   - Only insignificant commits (docs, style, etc.) were made
   - Solution: Make a feature or fix commit

2. **Version sync fails**
   - Check that main project files exist and have valid version tags
   - Ensure PowerShell execution policy allows script execution
   - Verify file permissions on setup project files

3. **Build failures**
   - Ensure Visual Studio build tools are properly installed
   - Check that all project dependencies are restored
   - Verify setup project references are correct

### Manual Verification
After release, verify:
1. Setup project versions match main project versions
2. New ProductCode GUIDs were generated
3. MSI files install and upgrade properly
4. GitHub release contains correct assets

## Best Practices

1. **Always test locally** before pushing to master
2. **Use conventional commits** for automatic versioning
3. **Review CHANGELOG.md** after versionize runs
4. **Test MSI installers** after version updates
5. **Keep setup project dependencies updated** with main projects

## Files Modified by This Process

### Automatically Updated
- `LeadTeams.Client/LeadTeams.Client.csproj` (by versionize)
- `LeadTeams.Desktop/LeadTeams.Desktop.csproj` (by versionize)
- `LeadTeamsClientSetup/LeadTeamsClientSetup.vdproj` (by sync script)
- `LeadTeamsDesktopSetup/LeadTeamsDesktopSetup.vdproj` (by sync script)
- `CHANGELOG.md` (by versionize)

### Generated Artifacts
- `LeadTeamsClientSetup/Release/LeadTeamsClientSetup.msi`
- `LeadTeamsDesktopSetup/Release/LeadTeamsDesktopSetup.msi`
- ZIP packages for GitHub releases
