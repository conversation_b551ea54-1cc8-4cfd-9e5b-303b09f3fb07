<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Version>1.8.1</Version>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0-windows|AnyCPU'">
    <NoWarn>1701;1702;CA1416</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0-windows|AnyCPU'">
    <NoWarn>1701;1702;CA1416</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="DataBaseAppSettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="JWTAppSettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="SessionAppSettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.3" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.3" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.7.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Authentications\GMCadiomCore.Authentications.csproj" />
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Repositories\GMCadiomCore.Repositories.csproj" />
    <ProjectReference Include="..\LeadTeams.Models\LeadTeams.Models.csproj" />
    <ProjectReference Include="..\LeadTeams.Services.Core\LeadTeams.Services.Core.csproj" />
  </ItemGroup>

</Project>
