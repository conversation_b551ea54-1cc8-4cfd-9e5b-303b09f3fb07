# Quick Release Guide

## Automated Release (Recommended)

1. **Make your changes** using conventional commits:
   ```bash
   git commit -m "feat: add new feature"
   git commit -m "fix: resolve bug"
   ```

2. **Push to master**:
   ```bash
   git push origin master
   ```

3. **GitHub Actions will automatically**:
   - Run versionize to increment versions
   - Sync setup project versions
   - Build installers
   - Create GitHub release

## Manual Release

1. **Run versionize locally**:
   ```bash
   dotnet tool install --global Versionize  # if not installed
   versionize --ignore-insignificant-commits
   ```

2. **Sync setup project versions**:
   ```bash
   .\UpdateVersions.bat
   # Choose option 1: Sync from main projects
   ```

3. **Build and test** setup projects in Visual Studio

4. **Push changes**:
   ```bash
   git push origin master --tags
   ```

## Quick Commands

### Sync versions only:
```powershell
.\UpdateSetupVersions.ps1 -SyncFromProjects
```

### Auto-increment setup versions:
```powershell
.\UpdateSetupVersions.ps1 -AutoIncrement
```

### Check current versions:
```powershell
# Main projects
Select-Xml -Path "LeadTeams.Client/LeadTeams.Client.csproj" -XPath "//Version"
Select-Xml -Path "LeadTeams.Desktop/LeadTeams.Desktop.csproj" -XPath "//Version"

# Setup projects  
Select-String -Path "LeadTeamsClientSetup/LeadTeamsClientSetup.vdproj" -Pattern "ProductVersion"
Select-String -Path "LeadTeamsDesktopSetup/LeadTeamsDesktopSetup.vdproj" -Pattern "ProductVersion"
```

## Conventional Commit Examples

```bash
# Patch release (1.0.0 → 1.0.1)
git commit -m "fix: resolve login timeout"
git commit -m "docs: update API documentation"
git commit -m "chore: update dependencies"

# Minor release (1.0.0 → 1.1.0)  
git commit -m "feat: add employee dashboard"
git commit -m "feat: implement new reporting feature"

# Major release (1.0.0 → 2.0.0)
git commit -m "feat: redesign authentication

BREAKING CHANGE: API endpoints have changed"
```

## Troubleshooting

- **No release created**: No significant commits since last version
- **Version sync fails**: Check file permissions and PowerShell execution policy
- **Build fails**: Ensure Visual Studio build tools are installed

## What Gets Updated

✅ **Automatically synchronized**:
- Main project versions (by versionize)
- Setup project versions (by sync script)
- ProductCode GUIDs (by sync script)
- CHANGELOG.md (by versionize)

✅ **Generated**:
- MSI installers
- GitHub release with assets
