"DeployProject"
{
"VSVersion" = "3:800"
"ProjectType" = "8:{978C614F-708E-4E1A-B201-565925725DBA}"
"IsWebType" = "8:FALSE"
"ProjectName" = "8:LeadTeamsClientSetup"
"LanguageId" = "3:1033"
"CodePage" = "3:1252"
"UILanguageId" = "3:1033"
"SccProjectName" = "8:"
"SccLocalPath" = "8:"
"SccAuxPath" = "8:"
"SccProvider" = "8:"
    "Hierarchy"
    {
        "Entry"
        {
        "MsmKey" = "8:_31DA26C00D1C45D4AF5784B95F9EEE8D"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FB3DC9FE2EBB41FD90EA27E2A473B347"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
    }
    "Configurations"
    {
        "Debug"
        {
        "DisplayName" = "8:Debug"
        "IsDebugOnly" = "11:TRUE"
        "IsReleaseOnly" = "11:FALSE"
        "OutputFilename" = "8:Debug\\LeadTeamsClientSetup.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-**********"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
                "Items"
                {
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:.NETFramework,Version=v4.7.2"
                    {
                    "Name" = "8:Microsoft .NET Framework 4.7.2 (x86 and x64)"
                    "ProductCode" = "8:.NETFramework,Version=v4.7.2"
                    }
                }
            }
        }
        "Release"
        {
        "DisplayName" = "8:Release"
        "IsDebugOnly" = "11:FALSE"
        "IsReleaseOnly" = "11:TRUE"
        "OutputFilename" = "8:Release\\LeadTeamsClientSetup.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-**********"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
                "Items"
                {
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:.NETFramework,Version=v4.7.2"
                    {
                    "Name" = "8:Microsoft .NET Framework 4.7.2 (x86 and x64)"
                    "ProductCode" = "8:.NETFramework,Version=v4.7.2"
                    }
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:Microsoft.NetCore.CoreRuntime.8.0.x64"
                    {
                    "Name" = "8:.NET Runtime 8.0.11 (x64)"
                    "ProductCode" = "8:Microsoft.NetCore.CoreRuntime.8.0.x64"
                    }
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:Microsoft.NetCore.CoreRuntime.8.0.x86"
                    {
                    "Name" = "8:.NET Runtime 8.0.11 (x86)"
                    "ProductCode" = "8:Microsoft.NetCore.CoreRuntime.8.0.x86"
                    }
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:Microsoft.NetCore.DesktopRuntime.8.0.x64"
                    {
                    "Name" = "8:.NET Desktop Runtime 8.0.11 (x64)"
                    "ProductCode" = "8:Microsoft.NetCore.DesktopRuntime.8.0.x64"
                    }
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:Microsoft.NetCore.DesktopRuntime.8.0.x86"
                    {
                    "Name" = "8:.NET Desktop Runtime 8.0.11 (x86)"
                    "ProductCode" = "8:Microsoft.NetCore.DesktopRuntime.8.0.x86"
                    }
                }
            }
        }
    }
    "Deployable"
    {
        "CustomAction"
        {
            "{4AA51A2D-7D85-4A59-BA75-B0809FC8B380}:_2D4C3947820846A381348DC3B3D3DB4C"
            {
            "Name" = "8:Publish Items from LeadTeams.Client (Active)"
            "Condition" = "8:"
            "Object" = "8:_FB3DC9FE2EBB41FD90EA27E2A473B347"
            "FileType" = "3:2"
            "InstallAction" = "3:4"
            "Arguments" = "8:/Uninstall"
            "EntryPoint" = "8:"
            "Sequence" = "3:1"
            "Identifier" = "8:_16CE980B_9B96_4932_909D_432B338957F5"
            "InstallerClass" = "11:FALSE"
            "CustomActionData" = "8:"
            }
            "{4AA51A2D-7D85-4A59-BA75-B0809FC8B380}:_3973DA17505B40EE83894C7EF5BE1FE4"
            {
            "Name" = "8:Publish Items from LeadTeams.Client (Active)"
            "Condition" = "8:"
            "Object" = "8:_FB3DC9FE2EBB41FD90EA27E2A473B347"
            "FileType" = "3:2"
            "InstallAction" = "3:1"
            "Arguments" = "8:/Install"
            "EntryPoint" = "8:"
            "Sequence" = "3:1"
            "Identifier" = "8:_C566675A_5387_4DD9_86A4_4C8F51EB837E"
            "InstallerClass" = "11:FALSE"
            "CustomActionData" = "8:"
            }
        }
        "DefaultFeature"
        {
        "Name" = "8:DefaultFeature"
        "Title" = "8:"
        "Description" = "8:"
        }
        "ExternalPersistence"
        {
            "LaunchCondition"
            {
                "{A06ECF26-33A3-4562-8140-9B0E340D4F24}:_BE967F6A0F584EFDB9EF0E9A3FBC1624"
                {
                "Name" = "8:.NET Core"
                "Message" = "8:[VSDNETCOREMSG]"
                "AllowLaterVersions" = "11:FALSE"
                "InstallUrl" = "8:https://dotnet.microsoft.com/download/dotnet-core/[NetCoreVerMajorDotMinor]"
                "IsNETCore" = "11:TRUE"
                "Architecture" = "2:0"
                "Runtime" = "2:0"
                }
            }
        }
        "File"
        {
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_31DA26C00D1C45D4AF5784B95F9EEE8D"
            {
            "SourcePath" = "8:..\\LeadTeams.Client\\icons8-monitoring-96.ico"
            "TargetName" = "8:icons8-monitoring-96.ico"
            "Tag" = "8:"
            "Folder" = "8:_F8DE847CD3504A84A1F22C8DACE0E033"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
        }
        "FileType"
        {
        }
        "Folder"
        {
            "{1525181F-901A-416C-8A58-119130FE478E}:_092FA1A4FC274417A220F5FAD73DFCB0"
            {
            "Name" = "8:#1928"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:StartupFolder"
                "Folders"
                {
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_0D27CB2583E24F53B6C578D70B434B78"
            {
            "Name" = "8:#1919"
            "AlwaysCreate" = "11:TRUE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:ProgramMenuFolder"
                "Folders"
                {
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_20724ACD21BA497192069AA25BAAFE9A"
            {
            "Name" = "8:#1921"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:StartMenuFolder"
                "Folders"
                {
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_7C5AEEDD2EE04E729F1413D89FEBAE1A"
            {
            "Name" = "8:#1916"
            "AlwaysCreate" = "11:TRUE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:DesktopFolder"
                "Folders"
                {
                }
            }
            "{3C67513D-01DD-4637-8A68-80971EB9504F}:_F8DE847CD3504A84A1F22C8DACE0E033"
            {
            "DefaultLocation" = "8:[ProgramFilesFolder][Manufacturer]\\[ProductName]"
            "Name" = "8:#1925"
            "AlwaysCreate" = "11:TRUE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:TARGETDIR"
                "Folders"
                {
                }
            }
        }
        "LaunchCondition"
        {
        }
        "Locator"
        {
        }
        "MsiBootstrapper"
        {
        "LangId" = "3:1033"
        "RequiresElevation" = "11:FALSE"
        }
        "Product"
        {
        "Name" = "8:Microsoft Visual Studio"
        "ProductName" = "8:LeadTeams"
        "ProductCode" = "8:{31523FF8-9E50-4A75-8266-8E9CFB11CBD0}"
        "PackageCode" = "8:{D442FE87-8FA1-4451-BEB5-4DC4532FF43B}"
        "UpgradeCode" = "8:{FF8BC2A0-EDF5-466A-BCDD-43560139E3BF}"
        "AspNetVersion" = "8:4.0.30319.0"
        "RestartWWWService" = "11:FALSE"
        "RemovePreviousVersions" = "11:TRUE"
        "DetectNewerInstalledVersion" = "11:TRUE"
        "InstallAllUsers" = "11:TRUE"
        "ProductVersion" = "8:1.6.0"
        "Manufacturer" = "8:SherifShalaby"
        "ARPHELPTELEPHONE" = "8:"
        "ARPHELPLINK" = "8:"
        "Title" = "8:LeadTeamsClientSetup"
        "Subject" = "8:"
        "ARPCONTACT" = "8:SherifShalaby"
        "Keywords" = "8:"
        "ARPCOMMENTS" = "8:"
        "ARPURLINFOABOUT" = "8:"
        "ARPPRODUCTICON" = "8:"
        "ARPIconIndex" = "3:0"
        "SearchPath" = "8:"
        "UseSystemSearchPath" = "11:TRUE"
        "TargetPlatform" = "3:0"
        "PreBuildEvent" = "8:"
        "PostBuildEvent" = "8:"
        "RunPostBuildEvent" = "3:0"
        }
        "Registry"
        {
            "HKLM"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_05A134CD9ECB4F529B7219796E69D1DA"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_469DC74506AE4982953955F3CB9E0B15"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCU"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_6262A1CF084941ECB5BD44AD20AD4914"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_956D28B021DE4E1F96CACED68A7FC840"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCR"
            {
                "Keys"
                {
                }
            }
            "HKU"
            {
                "Keys"
                {
                }
            }
            "HKPU"
            {
                "Keys"
                {
                }
            }
        }
        "Sequences"
        {
        }
        "Shortcut"
        {
            "{970C0BB2-C7D0-45D7-ABFA-7EC378858BC0}:_2A15F10E03DB49668C461494D8A1A0D3"
            {
            "Name" = "8:LeadTeams Client"
            "Arguments" = "8:"
            "Description" = "8:"
            "ShowCmd" = "3:1"
            "IconIndex" = "3:0"
            "Transitive" = "11:FALSE"
            "Target" = "8:_FB3DC9FE2EBB41FD90EA27E2A473B347"
            "Folder" = "8:_7C5AEEDD2EE04E729F1413D89FEBAE1A"
            "WorkingFolder" = "8:_F8DE847CD3504A84A1F22C8DACE0E033"
            "Icon" = "8:_31DA26C00D1C45D4AF5784B95F9EEE8D"
            "Feature" = "8:"
            }
            "{970C0BB2-C7D0-45D7-ABFA-7EC378858BC0}:_7980D2B2B0824FBAA3AE479D9AA79894"
            {
            "Name" = "8:LeadTeams Client"
            "Arguments" = "8:"
            "Description" = "8:"
            "ShowCmd" = "3:1"
            "IconIndex" = "3:0"
            "Transitive" = "11:FALSE"
            "Target" = "8:_FB3DC9FE2EBB41FD90EA27E2A473B347"
            "Folder" = "8:_20724ACD21BA497192069AA25BAAFE9A"
            "WorkingFolder" = "8:_F8DE847CD3504A84A1F22C8DACE0E033"
            "Icon" = "8:_31DA26C00D1C45D4AF5784B95F9EEE8D"
            "Feature" = "8:"
            }
            "{970C0BB2-C7D0-45D7-ABFA-7EC378858BC0}:_B5B9AB6EB1EA435B9E41639A2DC00200"
            {
            "Name" = "8:LeadTeams Client"
            "Arguments" = "8:"
            "Description" = "8:"
            "ShowCmd" = "3:1"
            "IconIndex" = "3:0"
            "Transitive" = "11:FALSE"
            "Target" = "8:_FB3DC9FE2EBB41FD90EA27E2A473B347"
            "Folder" = "8:_0D27CB2583E24F53B6C578D70B434B78"
            "WorkingFolder" = "8:_F8DE847CD3504A84A1F22C8DACE0E033"
            "Icon" = "8:_31DA26C00D1C45D4AF5784B95F9EEE8D"
            "Feature" = "8:"
            }
        }
        "UserInterface"
        {
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_16433D68367B46A2B8BE12CC712BE8F9"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdBasicDialogs.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_1657D77A574D425F81B5ABDDBEB8B30E"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:2"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_FD8489072DC149E985E055158993CB34"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_54A8EE2375CF4843A22142B4EF2C8674"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:2"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_5CC21972DFE042A3A7D65BA5BDB961F0"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_5E39A9C464B84183A865D0E0257C8332"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:2"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_2213C8C7798A485B8D402B4B7FC16EEF"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_33A94AECCE834B478B2EF8FF171EC4C2"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_3691CFAE8C1F4A71B04F9D60E95440F1"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_710ED7C0C1904670AF7C85B91742A194"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdUserInterface.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_A531E9888A80469388B75BAF3451D248"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:1"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_3E6B1F465DDE4FE898EA9FDC4C433A72"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_60B153441A734289AE078161264FA19E"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_9781180C8DA8444581E1E7F38B7144DC"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "InstallAllUsersVisible"
                            {
                            "Name" = "8:InstallAllUsersVisible"
                            "DisplayName" = "8:#1059"
                            "Description" = "8:#1159"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_BF317315494F4B29988FAF196293358D"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:1"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_7F0FF24A487744E7AD60146AE5640C63"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_CA09BD37186C446CA0368CCC0A556900"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:1"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_8F5390573BBD4F86BD8F9B110388A01B"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "UpdateText"
                            {
                            "Name" = "8:UpdateText"
                            "DisplayName" = "8:#1058"
                            "Description" = "8:#1158"
                            "Type" = "3:15"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1258"
                            "DefaultValue" = "8:#1258"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
        }
        "MergeModule"
        {
        }
        "ProjectOutput"
        {
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_FB3DC9FE2EBB41FD90EA27E2A473B347"
            {
            "SourcePath" = "8:..\\LeadTeams.Client\\obj\\Debug\\net8.0-windows\\apphost.exe"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_F8DE847CD3504A84A1F22C8DACE0E033"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:PublishItems"
            "OutputProjectGuid" = "8:{FFE28B6E-00EF-49D8-AF7E-D6F8FE8D4433}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
        }
    }
}
