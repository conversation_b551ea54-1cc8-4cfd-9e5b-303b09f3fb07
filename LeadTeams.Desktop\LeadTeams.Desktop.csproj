<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Version>1.8.1</Version>
    <StartupObject>LeadTeams.Desktop.Program</StartupObject>
    <ApplicationIcon>icons8-monitoring-96.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0-windows|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0-windows|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="icons8-monitoring-96.ico" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Tools\GMCadiomCore.Tools.csproj" />
    <ProjectReference Include="..\LeadTeams.Desktop.Services\LeadTeams.Desktop.Services.csproj" />
    <ProjectReference Include="..\LeadTeams.Services.API\LeadTeams.Services.API.csproj" />
    <ProjectReference Include="..\LeadTeams.Shared.Windows\LeadTeams.Shared.Windows.csproj" />
    <ProjectReference Include="..\LeadTeams.Shared\LeadTeams.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="CustomControls\EmployeeAllowanceDuration.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="CustomControls\DynamicPattern.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="CustomControls\PictureBoxSlider.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="Properties\Settings.Designer.cs">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Update="View\Employee\ManagementTeamListView.cs" />
    <Compile Update="View\Employee\AskLeaveListView.cs" />
    <Compile Update="View\Employee\ManagementTeamView.cs" />
    <Compile Update="View\Employee\AskLeaveView.cs" />
    <Compile Update="View\Allowance\AllowanceListView.cs" />
    <Compile Update="View\Allowance\AllowanceView.cs" />
    <Compile Update="View\Reports\EmployeeWithPayrollLogReport.cs" />
    <Compile Update="View\Employee\ScreensAccessProfileListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="View\Employee\ScreensAccessProfileView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="View\SystemConfiguration\SettingView.cs" />
    <Compile Update="View\Shift\ShiftListView.cs" />
    <Compile Update="View\Shift\ShiftView.cs" />
    <Compile Update="View\ScreenShotsMonitoring\ScreenShotsMonitoringListView.cs" />
    <Compile Update="View\Project\ProjectListView.cs" />
    <Compile Update="View\Project\ProjectView.cs" />
    <Compile Update="View\OrganizationNews\OrganizationNewsListView.cs" />
    <Compile Update="View\OrganizationNews\OrganizationNewsView.cs" />
    <Compile Update="View\Task\TaskListView.cs" />
    <Compile Update="View\Task\TaskView.cs" />
    <Compile Update="View\Meeting\MeetingListView.cs" />
    <Compile Update="View\Meeting\MeetingView.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Update="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <Target Name="Husky" BeforeTargets="Restore;CollectPackageReferences" Condition="'$(HUSKY)' != 0">
    <Exec Command="dotnet tool restore" StandardOutputImportance="Low" StandardErrorImportance="High" />
    <Exec Command="dotnet husky install" StandardOutputImportance="Low" StandardErrorImportance="High" WorkingDirectory=".." />
  </Target>
</Project>