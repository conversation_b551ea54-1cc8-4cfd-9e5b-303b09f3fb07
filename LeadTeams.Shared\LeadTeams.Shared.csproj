<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Version>1.8.1</Version>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0-windows|AnyCPU'">
    <NoWarn>1701;1702;CA1416</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0-windows|AnyCPU'">
    <NoWarn>1701;1702;CA1416</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GMCadiomCore\GMCadiomCore.Shared\GMCadiomCore.Shared.csproj" />
  </ItemGroup>
  <Target Name="Husky" BeforeTargets="Restore;CollectPackageReferences" Condition="'$(HUSKY)' != 0 and '$(IsCrossTargetingBuild)' == 'true'">
    <Exec Command="dotnet tool restore" StandardOutputImportance="Low" StandardErrorImportance="High" />
    <Exec Command="dotnet husky install" StandardOutputImportance="Low" StandardErrorImportance="High" WorkingDirectory=".." />
  </Target>
</Project>